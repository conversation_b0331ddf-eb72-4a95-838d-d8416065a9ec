.container {
    min-height: 100vh;
    background: linear-gradient(to bottom right, #faf5ff, #dbeafe);
    padding: 2rem 0;
    display: flex;
    justify-content: center;
}

.content {
    width: 100%;
    max-width: 1200px;
    padding-top: 4rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.titleSection {
    flex: 1;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.titleIcon {
    color: #8b5cf6;
}

.subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
}

.createButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #6366f1;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 1.5rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    text-decoration: none;
}


.createButton:hover {
    background: #6366f1;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.3);
    color: white;
}


.filtersCard {
    margin-bottom: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(139, 92, 246, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.searchContainer {
    flex: 1;
    min-width: 300px;
}

.searchInput {
    border-radius: 12px;
    border: 2px solid transparent;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.searchInput:focus,
.searchInput:focus-within {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.searchIcon {
    color: #8b5cf6;
}

/* 🆕 新增：日期选择器样式 */
.dateContainer {
    min-width: 280px;
}

.dateRangePicker {
    width: 100%;
    border-radius: 12px;
    border: 2px solid transparent;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.dateRangePicker:focus,
.dateRangePicker:focus-within {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.calendarIcon {
    color: #8b5cf6;
    width: 16px;
    height: 16px;
}

.sortContainer {
    min-width: 180px;
}

.sortSelect {
    width: 100%;
    border-radius: 12px;
}

.selectIcon {
    margin-right: 0.5rem;
    color: #8b5cf6;
}

.mainLayout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    align-items: start;
}

.postsSection {
    min-width: 0;
}

.postsContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.postCard {
    border-radius: 16px;
    border: 1px solid rgba(139, 92, 246, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.postCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
}

.postContent {
    display: flex;
    gap: 1rem;
    padding: 0.5rem;
    position: relative;
}

.avatarSection {
    flex-shrink: 0;
}

.avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid #8b5cf6;
    object-fit: cover;
}

.contentSection {
    flex: 1;
    min-width: 0;
}

.postHeader {
    margin-bottom: 0.75rem;
}

.postTitle {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
}

.postMeta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.authorName {
    font-weight: 500;
    color: #8b5cf6;
    font-size: 0.9rem;
}

.postDate {
    color: #6b7280;
    font-size: 0.85rem;
}

.xLink {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #8b5cf6;
    border: 1px solid #8b5cf6;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.xLink:hover {
    color: #8b5cf6;
    border-color: #8b5cf6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.xText {
    font-size: 0.8rem;
    white-space: nowrap;
}

.postDescription {
    color: #4b5563;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0 0 1rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 2.7rem;
}

.postFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.postFooterLeft {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    flex: 1;
}

.popularity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.starIcon {
    color: #fbbf24;
    width: 14px;
    height: 14px;
}

.viewCount {
    color: #6b7280;
    font-size: 0.8rem;
}

.tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 6px;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    margin: 0;
}

/* 卡片操作按钮样式 */
.cardActionButtons {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.cardActionBtn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    color: #6b7280;
    border: none;
    background: transparent;
    transition: all 0.2s ease;
    min-width: auto;
    height: auto;
}

.cardActionBtn:hover {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    transform: none;
}

.cardActionBtn.cardLiked {
    color: #dc2626;
}

.cardActionBtn.cardLiked:hover {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

.cardActionBtn.cardBookmarked {
    color: #d97706;
}

.cardActionBtn.cardBookmarked:hover {
    background: rgba(217, 119, 6, 0.1);
    color: #d97706;
}

.actionCount {
    font-size: 0.75rem;
    font-weight: 500;
}

.empty {
    padding: 4rem 2rem;
    text-align: center;
    color: #6b7280;
}


.sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    position: sticky;
    top: 2rem;
}

.sidebarCard {
    border-radius: 16px;
    border: 1px solid rgba(139, 92, 246, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.sidebarHeader {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.sidebarIcon {
    color: #8b5cf6;
    width: 18px;
    height: 18px;
}

.sidebarTitle {
    font-size: 1rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

/* 热门帖子 */
.hotPosts {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.hotPostItem {
    display: flex;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.hotPostItem:hover {
    background-color: #faf5ff;
}

.hotPostRank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
}

.hotPostContent {
    flex: 1;
    min-width: 0;
}

.hotPostTitle {
    font-size: 0.85rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hotPostMeta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.hotPostAuthor {
    color: #8b5cf6;
}

.hotPostViews {
    color: #6b7280;
}

/* 热门标签 */
.hotTags {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.hotTagItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.hotTagItem:hover {
    background-color: #faf5ff;
}

.hotTagName {
    font-size: 0.85rem;
    color: #111827;
    font-weight: 500;
}

.hotTagCount {
    font-size: 0.75rem;
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

/* 活跃用户 */
.activeUsers {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.activeUserItem {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.activeUserItem:hover {
    background-color: #faf5ff;
}

.activeUserAvatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #8b5cf6;
    object-fit: cover;
}

.activeUserInfo {
    flex: 1;
}

.activeUserName {
    font-size: 0.85rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.activeUserStats {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 社区统计 */
.communityStats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.statItem {
    text-align: center;
    padding: 0.75rem 0.5rem;
    border-radius: 8px;
    background: rgba(139, 92, 246, 0.05);
}

.statNumber {
    font-size: 1.25rem;
    font-weight: 700;
    color: #8b5cf6;
    margin-bottom: 0.25rem;
}

.statLabel {
    font-size: 0.75rem;
    color: #6b7280;
}

.postDetailModal {
    top: 20px;
}

.postDetailModal .ant-modal-content {
    border-radius: 16px;
    overflow: hidden;
}

.postDetailModal .ant-modal-body {
    padding: 0;
}

.postDetailMeta {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 4px;
    color: #666;
    font-size: 14px;
}

.postDetailTime,
.postDetailStat,
.postDetailTags {
    display: flex;
    align-items: center;
    gap: 4px;
}

.postDetailContent {
    padding: 2rem;
}

.postDetailHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f3f4f6;
    position: relative;
}

.postDetailAuthor {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.postDetailAvatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e5e7eb;
}

.postDetailAuthorInfo {
    display: flex;
    flex-direction: column;
}

.postDetailAuthorName {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.postDetailDate {
    color: #6b7280;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.postDetailXLink {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #8b5cf6;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid #8b5cf6;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.postDetailXLink:hover {
    background-color: #8b5cf6;
    color: white;
}

/* 帖子详情右上角操作区域 */
.postDetailTopRightActions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.75rem;
}

.postDetailActionButtons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.postDetailActionBtn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 40px;
    height: 36px;
    padding: 0 8px;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
}

.postDetailActionBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #8b5cf6;
    color: #8b5cf6;
}

.postDetailActionBtn.liked {
    background: #fef2f2;
    border-color: #f87171;
    color: #dc2626;
}

.postDetailActionBtn.liked:hover {
    background: #fee2e2;
    border-color: #ef4444;
    color: #dc2626;
}

.postDetailActionBtn.bookmarked {
    background: #fef3c7;
    border-color: #f59e0b;
    color: #d97706;
}

.postDetailActionBtn.bookmarked:hover {
    background: #fef3c7;
    border-color: #f59e0b;
    color: #d97706;
}

.postDetailTitle {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 1.5rem 0;
    line-height: 1.3;
}

.postDetailTag {
    color: #8b5cf6;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #8b5cf6;
    line-height: 1;
}

.postDetailBody {
    margin-bottom: 2rem;
}

.postDetailMarkdown {
    line-height: 1.7;
    color: #374151;
}

.mdH1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.mdH2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 1.5rem 0 0.75rem 0;
}

.mdH3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 1.25rem 0 0.5rem 0;
}

.mdP {
    margin: 0.75rem 0;
    line-height: 1.7;
}

.mdLi {
    margin: 0.25rem 0;
    margin-left: 1.5rem;
    list-style-type: disc;
}

.mdCode {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.875rem;
}

.postDetailDescription {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #4b5563;
}

.postDetailFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* padding-top: 1.5rem; */
    /* border-top: 2px solid #f3f4f6; */
    flex-wrap: wrap;
    gap: 1rem;
}

.postDetailStats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.postDetailStat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.postDetailStat svg {
    width: 16px;
    height: 16px;
}

.postDetailActions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.postDetailActionBtn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.postDetailActionBtn:hover {
    transform: translateY(-1px);
}

.createModal {
    border-radius: 16px;
}

.createForm {
    margin-top: 1rem;
}

.formActions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    margin-bottom: 0;
}

.submitButton {
    background-color: #6366f1;
    color: white;
    border: none;
    font-weight: 600;
}

.listBottomControls {
    max-width: 1200px;
    margin: 0 auto;
    margin-bottom: 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem 0.5rem 0 0.5rem;
}

.bottomPagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.resultsInfo {
    font-size: 0.875rem;
    color: #6b7280;
}

.compactPagination {
    font-size: 12px;
}



/* 标签相关样式 - 使用活动页面的样式 */
.tagsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.selectedTag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    border: none;
    margin: 0;
}

.removeTagButton {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.removeTagButton:hover {
    color: #374151;
}

.tagInput {
    width: 80px;
    height: 28px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    outline: none;
}

.tagInput:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.addTagButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: transparent;
    color: #8b5cf6;
    border: 1px dashed #8b5cf6;
    border-radius: 9999px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.addTagButton:hover {
    background-color: #faf5ff;
}

/* 响应式设计 */
@media (max-width: 1280px) {
    .content {
        max-width: 1000px;
    }
}

@media (max-width: 1024px) {
    .content {
        max-width: 800px;
    }

    .mainLayout {
        grid-template-columns: 1fr;
    }

    .sidebar {
        order: -1;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .dateContainer {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem 0;
    }

    .content {
        padding: 0 1rem;
    }

    .header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .title {
        font-size: 2rem;
        justify-content: center;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .searchContainer {
        min-width: auto;
    }

    .sortContainer {
        min-width: auto;
    }

    .postContent {
        gap: 0.75rem;
    }

    .avatar {
        width: 40px;
        height: 40px;
    }

    .postTitle {
        font-size: 1rem;
    }

    .postMeta {
        gap: 0.5rem;
    }

    .postFooter {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .postFooterLeft {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        width: 100%;
    }

    .cardActionButtons {
        align-self: flex-end;
        margin-top: 0.5rem;
    }

    .formActions {
        flex-direction: column;
    }

    .xText {
        display: none;
    }

    .xLink {
        padding: 0.25rem 0.5rem;
        min-width: auto;
    }

    .xLinkTopRight {
        padding: 0.25rem 0.5rem;
        min-width: auto;
    }

    .xLinkTopRight .xText {
        display: none;
    }

    /* 帖子详情页面响应式 */
    .postDetailTopRightActions {
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }

    .postDetailActionButtons {
        gap: 0.25rem;
    }

    .postDetailActionBtn {
        min-width: 36px;
        height: 32px;
        padding: 0 6px;
    }

    .postDetailXLink span {
        display: none;
    }

    .postDetailXLink {
        padding: 0.5rem;
        min-width: auto;
    }
}

/* 右上角区域样式 */
.topRightActions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10;
}

.xLinkTopRight {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #8b5cf6;
    border: 1px solid #8b5cf6;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

.xLinkTopRight:hover {
    color: #8b5cf6;
    border-color: #8b5cf6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    background: rgba(255, 255, 255, 1);
}

.actionButtons {
    display: flex;
    gap: 4px;
}

.editButton {
    border: none;
    background: transparent;
    color: #8b5cf6;
    cursor: pointer;
    padding: 4px;
}

.deleteButton {
    border: none;
    background: transparent;
    color: #ff4d4f;
    cursor: pointer;
    padding: 4px;
}

.editButton:hover {
    color: #8b5cf6  !important;
}

.deleteButton:hover {
    color: #ff4d4f !important;
}

