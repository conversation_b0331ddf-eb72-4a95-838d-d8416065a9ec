import type React from 'react';
import debounce from 'lodash/debounce';
import { useState, useMemo, useEffect, useCallback } from 'react';
import {
  Pagination,
  Input,
  Select,
  Card,
  Empty,
  Button,
  Modal,
  Form,
  message,
  Spin,
  Tag,
  DatePicker,
  App as AntdApp,
  Popconfirm,
} from 'antd';
import {
  Search,
  Star,
  Plus,
  User,
  ExternalLink,
  Clock,
  X,
  TrendingUp,
  Users,
  MessageCircle,
  Calendar,
  ThumbsUp,
  Share2,
  Eye,
  Edit,
  Trash2,
  Heart,
  Bookmark,
} from 'lucide-react';
import styles from './index.module.css';
import {
  getPosts,
  createPost,
  Post as PostType,
  getPostsStats,
  PostsStats,
  Post,
  getPostById,
  updatePost,
  deletePost,
} from '../api/post';
import { SiX } from 'react-icons/si';
import Image from 'next/image';
import DateButton from '@/components/base/DateButton';

import dayjs from 'dayjs';
import VditorEditor from '@/components/vditorEditor';
import { sanitizeMarkdown,parseMarkdown } from '@/lib/markdown';
import { useAuth } from '@/contexts/AuthContext';

const { Option } = Select;
const { RangePicker } = DatePicker;

export default function PostsList() {
  const { message } = AntdApp.useApp();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [posts, setPosts] = useState<PostType[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('desc');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingPost, setEditingPost] = useState<PostType | null>(null);

  const [form] = Form.useForm();
  const [tags, setTags] = useState<string[]>([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [dateRange, setDateRange] = useState<
    [dayjs.Dayjs | null, dayjs.Dayjs | null]
  >(() => {
    const endOfToday = dayjs().endOf('day');
    const startOfWeekAgo = dayjs().subtract(6, 'day').startOf('day');
    return [startOfWeekAgo, endOfToday];
  });
  const [postsStats, setPostsStats] = useState<PostsStats | null>(null);
  const [isPostDetailVisible, setIsPostDetailVisible] = useState(false);
  const [selectedPost, setSelectedPost] = useState<PostType | null>(null);
  const [startDate, setStartDate] = useState(
    dateRange[0]?.format('YYYY-MM-DD')
  );
  const [endDate, setEndDate] = useState(dateRange[1]?.format('YYYY-MM-DD'));

  const [loading, setLoading] = useState(false);
  const [btnLoading, setbtnLoading] = useState(false);
  const [detailLoading, setdetailLoading] = useState(false);

  // 点赞和收藏状态 - 详情页
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);

  // 帖子列表的点赞收藏状态 - 使用Map存储每个帖子的状态
  const [postLikeStates, setPostLikeStates] = useState<Map<number, boolean>>(new Map());
  const [postBookmarkStates, setPostBookmarkStates] = useState<Map<number, boolean>>(new Map());
  const [postLikeCounts, setPostLikeCounts] = useState<Map<number, number>>(new Map());

  const { session, status } = useAuth();
  const permissions = session?.user?.permissions || [];

  // parseMarkdown将返回的markdown转为html展示
  const [postContent, setPostContent] = useState<string>('');

  useEffect(() => {
    if (selectedPost?.description) {
      parseMarkdown(selectedPost.description).then((htmlContent) => {
        console.log(htmlContent);
        
        setPostContent(htmlContent);
      });
    }
  }, [selectedPost?.description]);

  const fetchPosts = useCallback(
    async (params?: {
      keyword?: string;
      order?: 'asc' | 'desc';
      page?: number;
      page_size?: number;
      start_date?: string;
      end_date?: string;
    }) => {
      setLoading(true);

      const res = await getPosts({
        keyword: params?.keyword || searchTerm,
        order: params?.order || (sortBy as 'asc' | 'desc'),
        page: params?.page || currentPage,
        page_size: params?.page_size || pageSize,
        start_date: params?.start_date || startDate,
        end_date: params?.end_date || endDate,
      });
      if (res.success && res.data) {
        setPosts(res.data.posts);
        setTotal(res.data.total || res.data.posts.length);
      } else {
        message.error(res.message || '获取帖子失败');
      }

      setLoading(false);
    },
    [searchTerm, sortBy, startDate, endDate]
  );

  const fetchPostsStats = async () => {
    try {
      const res = await getPostsStats();
      if (res.success && res.data) {
        setPostsStats(res.data);
      } else {
        message.error(res.message || '获取社区统计失败');
      }
    } catch (error) {
      console.error('获取社区统计异常:', error);
      message.error('获取社区统计异常');
    }
  };

  useEffect(() => {
  // 防抖
  const debouncedFetch = debounce(() => {
    fetchPosts();
    fetchPostsStats();
  }, 300);

  setCurrentPage(1);

  let newStartDate = undefined;
  let newEndDate = undefined;
  if (!dateRange?.[0] || !dateRange?.[1]) {
    newStartDate = undefined;
    newEndDate = undefined;
  } else {
    newStartDate = dateRange[0].format('YYYY-MM-DD');
    newEndDate = dateRange[1].format('YYYY-MM-DD');
  }

  setStartDate(newStartDate);
  setEndDate(newEndDate);

  debouncedFetch();

  return () => {
    debouncedFetch.cancel();
  };
}, [searchTerm, sortBy, dateRange, fetchPosts]);

  const getStarCount = (viewCount: number) => {
    if (viewCount >= 2000) return 5;
    if (viewCount >= 1500) return 4;
    if (viewCount >= 1000) return 3;
    if (viewCount >= 500) return 2;
    return 1;
  };

  const handleCallPost = async (values: any) => {
    try {
       setbtnLoading(true)
      if (isEditMode && editingPost) {
        const res = await updatePost(editingPost.ID.toString(), {
          title: values.title,
          description: values.description,
          tags,
          twitter: values.twitter,
        });
        if (res.success) {
          message.success('帖子更新成功！');
        } else {
          message.error(res.message || '更新失败');
        }
      } else {
        const res = await createPost({
          title: values.title,
          description: values.description,
          tags,
          twitter: values.twitter,
        });
        if (res.success) {
          message.success('帖子发布成功！');
        } else {
          message.error(res.message || '发布失败');
        }
      }

      setIsCreateModalVisible(false);
      setIsEditMode(false);
      setEditingPost(null);
      form.resetFields();
      fetchPosts();
      fetchPostsStats();
    } catch (error) {
      message.error('操作失败，请重试');
    } finally{
      setbtnLoading(false)
    }
  };

  const handleEditPost = (post: PostType) => {
    setIsEditMode(true);
    setEditingPost(post);
    setIsCreateModalVisible(true);

    // 填充表单
    form.setFieldsValue({
      title: post.title,
      description: post.description,
      twitter: post.twitter || '',
    });
    setTags(post.tags || []);
  };

  const handleDeletePost = async (postId: number) => {
    try {
      const res = await deletePost(postId);
      if (res.success) {
        message.success('帖子删除成功');
        fetchPosts();
        fetchPostsStats();
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败，请重试');
    }
  };

  // 编辑器处理
  const handleVditorEditorChange = useCallback(
    (value: string) => {
      form.setFieldValue('description', value);
    },
    [form]
  );

  const handleAddTag = () => {
    if (inputValue && !tags.includes(inputValue)) {
      const newTags = [...tags, inputValue];
      setTags(newTags);
      setInputValue('');
    }
    setInputVisible(false);
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(newTags);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handlePostClick = async (post: Post) => {
    try {
      setIsPostDetailVisible(true);
      setdetailLoading(true);
      // 直接从列表拿数据
      // const currentPost = posts.find((item: Post) => item.ID === post.ID);
      // if (currentPost) {
      //   setSelectedPost(currentPost);
      // } else {
      //   console.error('获取帖子失败:');
      // }

      const res = await getPostById(post.ID.toString());
      if (res.success && res.data) {
        setSelectedPost(res.data);
      } else {
        console.error('获取帖子失败:', res.message);
      }
    } catch (error) {
      console.error('获取帖子详情异常:', error);
    } finally {
      setdetailLoading(false);
    }
  };

  const handleClosePostDetail = () => {
    setPostContent('');
    setIsPostDetailVisible(false);
    setSelectedPost(null);
    // 重置点赞和收藏状态
    setIsLiked(false);
    setIsBookmarked(false);
    setLikeCount(0);
    // fetchPosts();
    // fetchPostsStats();
  };

  // 点赞处理函数
  const handleLike = async () => {
    if (!selectedPost) return;

    try {
      // TODO: 调用后端点赞接口
      // const res = await likePost(selectedPost.ID);
      // if (res.success) {
        setIsLiked(!isLiked);
        setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
        message.success(isLiked ? '取消点赞成功' : '点赞成功');
      // } else {
      //   message.error(res.message || '操作失败');
      // }
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 收藏处理函数
  const handleBookmark = async () => {
    if (!selectedPost) return;

    try {
      // TODO: 调用后端收藏接口
      // const res = await bookmarkPost(selectedPost.ID);
      // if (res.success) {
        setIsBookmarked(!isBookmarked);
        message.success(isBookmarked ? '取消收藏成功' : '收藏成功');
      // } else {
      //   message.error(res.message || '操作失败');
      // }
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 列表卡片点赞处理函数
  const handleCardLike = async (postId: number, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    try {
      // TODO: 调用后端点赞接口
      // const res = await likePost(postId);
      // if (res.success) {
        const currentLiked = postLikeStates.get(postId) || false;
        const currentCount = postLikeCounts.get(postId) || 0;

        setPostLikeStates(new Map(postLikeStates.set(postId, !currentLiked)));
        setPostLikeCounts(new Map(postLikeCounts.set(postId, currentLiked ? currentCount - 1 : currentCount + 1)));

        message.success(currentLiked ? '取消点赞成功' : '点赞成功');
      // } else {
      //   message.error(res.message || '操作失败');
      // }
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 列表卡片收藏处理函数
  const handleCardBookmark = async (postId: number, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    try {
      // TODO: 调用后端收藏接口
      // const res = await bookmarkPost(postId);
      // if (res.success) {
        const currentBookmarked = postBookmarkStates.get(postId) || false;
        setPostBookmarkStates(new Map(postBookmarkStates.set(postId, !currentBookmarked)));

        message.success(currentBookmarked ? '取消收藏成功' : '收藏成功');
      // } else {
      //   message.error(res.message || '操作失败');
      // }
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  const handleDateRangeChange = (
    dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null
  ) => {
    if (!dates || !dates[0] || !dates[1]) {
      setDateRange([null, null]);
    } else {
      setDateRange([dates[0], dates[1]]);
    }
  };

  // pagenation
  // 分页处理
  const handlePageChange = async (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
    await fetchPosts({ page, page_size: size || pageSize });
  };

  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, total);

  return (
    <div className={`${styles.container} nav-t-top`}>
      <div className={styles.content}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h1 className={styles.title}>
              <User className={styles.titleIcon} />
              社区帖子
            </h1>
            <p className={styles.subtitle}>分享见解，交流经验，共建社区</p>
          </div>
          {status === 'authenticated' && permissions.includes('blog:write') && (
            <Button
              type="primary"
              icon={<Plus size={16} />}
              className={styles.createButton}
              onClick={() => setIsCreateModalVisible(true)}
            >
              发布帖子
            </Button>
          )}
        </div>
        <Card className={styles.filtersCard}>
          <div className={styles.filters}>
            <div className={styles.searchContainer}>
              <Input
                placeholder="搜索帖子、作者..."
                prefix={<Search className={styles.searchIcon} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
                size="large"
              />
            </div>
            <div className={styles.dateContainer}>
              <RangePicker
                prefix={
                  <>
                    <DateButton
                      style={{ marginRight: '4px' }}
                      size="small"
                      color="primary"
                      variant="filled"
                      dateRange={dateRange}
                      handleDateRangeChange={handleDateRangeChange}
                      label="今天"
                      dates={[dayjs(), dayjs()]}
                      active={
                        dateRange[0]?.format('YYYY-MM-DD') ===
                          dayjs().format('YYYY-MM-DD') &&
                        dateRange[1]?.format('YYYY-MM-DD') ===
                          dayjs().format('YYYY-MM-DD')
                      }
                    />
                    <DateButton
                      size="small"
                      color="primary"
                      variant="filled"
                      dateRange={dateRange}
                      handleDateRangeChange={handleDateRangeChange}
                      label="近一周"
                      dates={[dayjs().subtract(1, 'week'), dayjs()]}
                      active={
                        dateRange[0]?.format('YYYY-MM-DD') ===
                          dayjs().subtract(1, 'week').format('YYYY-MM-DD') &&
                        dateRange[1]?.format('YYYY-MM-DD') ===
                          dayjs().format('YYYY-MM-DD')
                      }
                    />
                  </>
                }
                placeholder={['开始日期', '结束日期']}
                value={dateRange}
                onChange={handleDateRangeChange}
                className={styles.dateRangePicker}
                size="large"
                suffixIcon={<Calendar className={styles.calendarIcon} />}
                format="YYYY-MM-DD"
                allowClear
              />
            </div>
            <div className={styles.sortContainer}>
              <Select
                value={sortBy}
                onChange={setSortBy}
                className={styles.sortSelect}
                size="large"
              >
                <Option value="desc">最新发布</Option>
                <Option value="asc">最早发布</Option>
              </Select>
            </div>
            {/* <div className={styles.resultsInfo}>
              显示 {startIndex}-{endIndex} 项，共 {total} 项
            </div> */}
          </div>
        </Card>

        <Spin spinning={loading}>
          <div className={styles.mainLayout}>
            <div className={styles.postsSection}>
              <div className={styles.postsContainer}>
                {posts.length === 0 ? (
                  <Empty description="暂无帖子" className={styles.empty} />
                ) : (
                  posts.map((post) => (
                    <Card
                      key={post.ID}
                      className={styles.postCard}
                      onClick={() => handlePostClick(post)}
                    >
                      <div className={styles.postContent}>
                        {/* 帖子头部：作者信息和操作按钮 */}
                        <div className={styles.postHeader}>
                          <div className={styles.authorSection}>
                            <Image
                              src={post.user?.avatar || '/placeholder.svg'}
                              alt={post.user?.username || 'avatar'}
                              width={36}
                              height={36}
                              className={styles.avatar}
                            />
                            <div className={styles.authorInfo}>
                              <span className={styles.authorName}>
                                {post.user?.username}
                              </span>
                              <span className={styles.postDate}>
                                {dayjs(post.CreatedAt).format('YYYY-MM-DD HH:mm')}
                              </span>
                            </div>
                          </div>

                          {/* 右侧操作按钮 */}
                          <div className={styles.headerActions}>
                            {post.twitter && (
                              <a
                                href={post.twitter}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={styles.twitterLink}
                                onClick={(e) => e.stopPropagation()}
                                title="查看推文"
                              >
                                <SiX size={14} />
                              </a>
                            )}

                            {status === 'authenticated' &&
                              session?.user?.uid == post.user?.ID && (
                                <div className={styles.ownerActions}>
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<Edit size={14} />}
                                    className={styles.editButton}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditPost(post);
                                    }}
                                  />
                                  <Popconfirm
                                    title="确认删除该帖子吗？"
                                    description="删除后将无法恢复"
                                    okText="删除"
                                    cancelText="取消"
                                    okButtonProps={{ danger: true }}
                                    onConfirm={(e) => {
                                      e?.stopPropagation();
                                      handleDeletePost(post.ID);
                                    }}
                                    onCancel={(e) => e?.stopPropagation()}
                                  >
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<Trash2 size={14} />}
                                      className={styles.deleteButton}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  </Popconfirm>
                                </div>
                              )}
                          </div>
                        </div>

                        {/* 帖子标题 */}
                        <h3 className={styles.postTitle}>{post.title}</h3>

                        {/* 帖子描述 */}
                        <p className={styles.postDescription}>
                          {post.description}
                        </p>

                        {/* 帖子底部：标签和互动数据 */}
                        <div className={styles.postFooter}>
                          <div className={styles.tagsSection}>
                            {post.tags.slice(0, 3).map((tag, index) => (
                              <span key={index} className={styles.tag}>
                                {tag}
                              </span>
                            ))}
                            {post.tags.length > 3 && (
                              <span className={styles.moreTagsIndicator}>
                                +{post.tags.length - 3}
                              </span>
                            )}
                          </div>

                          <div className={styles.interactionSection}>
                            {/* 浏览量 */}
                            {post.view_count !== 0 && (
                              <div className={styles.viewCount}>
                                <Eye size={14} />
                                <span>{post.view_count?.toLocaleString()}</span>
                              </div>
                            )}

                            {/* 点赞按钮 */}
                            <Button
                              type="text"
                              size="small"
                              icon={
                                <Heart
                                  size={14}
                                  fill={postLikeStates.get(post.ID) ? 'currentColor' : 'none'}
                                />
                              }
                              className={`${styles.interactionBtn} ${
                                postLikeStates.get(post.ID) ? styles.liked : ''
                              }`}
                              onClick={(e) => handleCardLike(post.ID, e)}
                            >
                              {(postLikeCounts.get(post.ID) || 0) > 0 && (
                                <span>{postLikeCounts.get(post.ID)}</span>
                              )}
                            </Button>

                            {/* 收藏按钮 */}
                            <Button
                              type="text"
                              size="small"
                              icon={
                                <Bookmark
                                  size={14}
                                  fill={postBookmarkStates.get(post.ID) ? 'currentColor' : 'none'}
                                />
                              }
                              className={`${styles.interactionBtn} ${
                                postBookmarkStates.get(post.ID) ? styles.bookmarked : ''
                              }`}
                              onClick={(e) => handleCardBookmark(post.ID, e)}
                            />
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </div>

            <div className={styles.sidebar}>
              {/* 热门帖子 */}
              <Card className={styles.sidebarCard}>
                <div className={styles.sidebarHeader}>
                  <TrendingUp className={styles.sidebarIcon} />
                  <h3 className={styles.sidebarTitle}>热门帖子</h3>
                </div>
                <div className={styles.hotPosts}>
                  {(postsStats?.weekly_hot_posts ?? []).map((post, index) => (
                    <div
                      key={post.ID}
                      className={styles.hotPostItem}
                      onClick={() => handlePostClick(post)}
                    >
                      <div className={styles.hotPostRank}>{index + 1}</div>
                      <div className={styles.hotPostContent}>
                        <h4 className={styles.hotPostTitle}>{post.title}</h4>
                        <div className={styles.hotPostMeta}>
                          <span className={styles.hotPostAuthor}>
                            {post.user?.username}
                          </span>
                          <span className={styles.hotPostViews}>
                            {post.view_count} 浏览
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {(postsStats?.weekly_hot_posts?.length ?? 0) === 0 && (
                    <Empty description="暂无热门帖子" />
                  )}
                </div>
              </Card>

              {/* 活跃用户 */}
              <Card className={styles.sidebarCard}>
                <div className={styles.sidebarHeader}>
                  <Users className={styles.sidebarIcon} />
                  <h3 className={styles.sidebarTitle}>活跃作者</h3>
                </div>
                <div className={styles.activeUsers}>
                  {(postsStats?.top_active_users ?? []).map((user) => (
                    <div key={user.ID} className={styles.activeUserItem}>
                      <Image
                        width={40} // 你可以根据实际样式调整宽度
                        height={40}
                        src={user.avatar || '/placeholder.svg'}
                        alt={user.username}
                        className={styles.activeUserAvatar}
                      />
                      <div className={styles.activeUserInfo}>
                        <div className={styles.activeUserName}>
                          {user.username}
                        </div>
                        <div className={styles.activeUserPosts}>
                          帖子数: {user.post_count}
                        </div>
                      </div>
                    </div>
                  ))}
                  {(postsStats?.top_active_users?.length ?? 0) === 0 && (
                    <Empty description="暂无活跃用户" />
                  )}
                </div>
              </Card>

              {/* 社区统计 */}
              <Card className={styles.sidebarCard}>
                <div className={styles.sidebarHeader}>
                  <MessageCircle className={styles.sidebarIcon} />
                  <h3 className={styles.sidebarTitle}>社区统计</h3>
                </div>
                <div className={styles.communityStats}>
                  <div className={styles.statItem}>
                    <div className={styles.statNumber}>
                      {postsStats?.total_posts?.toLocaleString() ?? '0'}
                    </div>
                    <div className={styles.statLabel}>总帖子数</div>
                  </div>
                  <div className={styles.statItem}>
                    <div className={styles.statNumber}>
                      {postsStats?.active_user_count?.toLocaleString() ?? '0'}
                    </div>
                    <div className={styles.statLabel}>活跃用户</div>
                  </div>
                  <div className={styles.statItem}>
                    <div className={styles.statNumber}>
                      {postsStats?.weekly_post_count?.toLocaleString() ?? '0'}
                    </div>
                    <div className={styles.statLabel}>本周帖子</div>
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className={styles.listBottomControls}>
            <div className={styles.bottomPagination}>
              <Pagination
                current={currentPage}
                total={total}
                pageSize={pageSize}
                onChange={handlePageChange}
                // showQuickJumper={true}
                showTotal={(total, range) =>
                  `显示 ${startIndex}-${endIndex} 项，共 ${total} 项`
                }
                className={styles.fullPagination}
              />
            </div>
          </div>
        </Spin>

        <Modal
          loading={detailLoading}
          title={null}
          open={isPostDetailVisible}
          onCancel={handleClosePostDetail}
          footer={null}
          width={800}
          className={styles.postDetailModal}
        >
          {selectedPost && (
            <div className={styles.postDetailContent}>
              {/* 帖子头部 */}
              <div className={styles.postDetailHeader}>
                {/* 左侧作者信息区域 */}
                <div className={styles.postDetailAuthorSection}>
                  <Image
                    src={selectedPost.user?.avatar || '/placeholder.svg'}
                    width={48}
                    height={48}
                    alt={selectedPost.user?.username as string}
                    className={styles.postDetailAvatar}
                  />
                  <div className={styles.postDetailAuthorInfo}>
                    <h4 className={styles.postDetailAuthorName}>
                      {selectedPost.user?.username}
                    </h4>

                    {/* 元数据第一行：时间和浏览量 */}
                    <div className={styles.postDetailMetaRow}>
                      <div className={styles.postDetailTime}>
                        <Clock size={14} />
                        <span>
                          {dayjs(selectedPost.CreatedAt).format('YYYY-MM-DD HH:mm')}
                        </span>
                      </div>

                      {selectedPost.view_count !== 0 && (
                        <div className={styles.postDetailViewCount}>
                          <Eye size={14} />
                          <span>{selectedPost.view_count?.toLocaleString()} 浏览</span>
                        </div>
                      )}
                    </div>

                    {/* 元数据第二行：标签 */}
                    <div className={styles.postDetailTagsRow}>
                      {selectedPost.tags.slice(0, 4).map((tag, index) => (
                        <span key={index} className={styles.postDetailTag}>
                          {tag}
                        </span>
                      ))}
                      {selectedPost.tags.length > 4 && (
                        <span className={styles.postDetailMoreTags}>
                          +{selectedPost.tags.length - 4}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* 右侧操作区域 */}
                <div className={styles.postDetailActions}>
                  {/* 主要操作按钮 */}
                  <div className={styles.postDetailMainActions}>
                    <Button
                      type="text"
                      size="large"
                      icon={<Heart size={18} fill={isLiked ? 'currentColor' : 'none'} />}
                      className={`${styles.postDetailActionBtn} ${isLiked ? styles.detailLiked : ''}`}
                      onClick={handleLike}
                    >
                      {likeCount > 0 ? likeCount : '点赞'}
                    </Button>

                    <Button
                      type="text"
                      size="large"
                      icon={<Bookmark size={18} fill={isBookmarked ? 'currentColor' : 'none'} />}
                      className={`${styles.postDetailActionBtn} ${isBookmarked ? styles.detailBookmarked : ''}`}
                      onClick={handleBookmark}
                    >
                      收藏
                    </Button>
                  </div>

                  {/* 次要操作 */}
                  <div className={styles.postDetailSecondaryActions}>
                    {selectedPost.twitter && (
                      <a
                        href={selectedPost.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={styles.postDetailTwitterLink}
                        title="查看推文"
                      >
                        <SiX size={16} />
                        <span>推文</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* 帖子标题 */}
              <h1 className={styles.postDetailTitle}>{selectedPost.title}</h1>

              {/* 帖子内容 */}
              <div className={styles.postDetailBody}>
                <div
                  className="prose"
                  dangerouslySetInnerHTML={{ __html: postContent }}
                />
              </div>

              {/* 帖子底部：互动统计和分享 */}
              <div className={styles.postDetailFooter}>
                <div className={styles.postDetailStats}>
                  {selectedPost.view_count !== 0 && (
                    <div className={styles.postDetailStatItem}>
                      <Eye size={16} />
                      <span>{selectedPost.view_count?.toLocaleString()} 次浏览</span>
                    </div>
                  )}

                  <div className={styles.postDetailStatItem}>
                    <Heart size={16} />
                    <span>{likeCount || 0} 次点赞</span>
                  </div>

                  <div className={styles.postDetailStatItem}>
                    <MessageCircle size={16} />
                    <span>0 条评论</span>
                  </div>
                </div>

                <div className={styles.postDetailShareActions}>
                  <Button
                    type="text"
                    icon={<Share2 size={16} />}
                    className={styles.postDetailShareBtn}
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: 实现分享功能
                      message.info('分享功能开发中...');
                    }}
                  >
                    分享
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          title={isEditMode ? '编辑帖子' : '发布新帖子'}
          open={isCreateModalVisible}
          onCancel={() => {
            setIsCreateModalVisible(false);
            setIsEditMode(false);
            setEditingPost(null);
            form.resetFields();
          }}
          footer={null}
          width={800}
          className={styles.createModal}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCallPost}
            className={styles.createForm}
          >
            <Form.Item
              name="title"
              label="标题"
              rules={[
                { required: true, message: '请输入帖子标题' },
                { max: 100, message: '标题不能超过100个字符' },
              ]}
            >
              <Input placeholder="输入帖子标题..." size="large" />
            </Form.Item>

            <Form.Item
              name="description"
              label="内容描述"
              rules={[
                { required: true, message: '请输入帖子内容' },
                { min: 100, message: '内容至少需要100个字符' },
                { max: 2000, message: '内容不能超过2000个字符' },
              ]}
            >
              <VditorEditor
                value={form.getFieldValue('description')}
                onChange={handleVditorEditorChange}
              />
            </Form.Item>
            <Form.Item
              name="twitter"
              label="推文链接"
              rules={[
                {
                  type: 'url',
                  message: '请输入有效的 URL 链接',
                },
              ]}
            >
              <Input placeholder="输入推文链接" size="large" />
            </Form.Item>
            <Form.Item label="标签">
              <div className={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <span key={index} className={styles.selectedTag}>
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className={styles.removeTagButton}
                    >
                      <X size={12} />
                    </button>
                  </span>
                ))}
                {inputVisible ? (
                  <input
                    type="text"
                    className={styles.tagInput}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onBlur={handleAddTag}
                    onKeyPress={handleKeyPress}
                    autoFocus
                  />
                ) : (
                  <button
                    type="button"
                    onClick={() => setInputVisible(true)}
                    className={styles.addTagButton}
                  >
                    <Plus size={14} />
                    添加标签
                  </button>
                )}
              </div>
            </Form.Item>

            <Form.Item className={styles.formActions}>
              <div className={styles.formActions}>
                <Button
                  onClick={() => {
                    setIsCreateModalVisible(false);
                    form.resetFields();
                  }}
                >
                  取消
                </Button>
                <Button 
                  loading={btnLoading}
                  type="primary"
                  htmlType="submit"
                  className={styles.submitButton}
                >
                  {isEditMode ? '更新帖子' : '发布帖子'}
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
}
