# 点赞收藏功能实现总结

## 功能描述
在帖子详情模态框的右上角和帖子列表卡片的右下角添加了点赞和收藏按钮，目前接口留空，等待后端接口实现。

## 实现的功能

### 1. 新增状态管理
**详情页状态：**
- `isLiked`: 详情页点赞状态
- `isBookmarked`: 详情页收藏状态
- `likeCount`: 详情页点赞数量

**列表页状态：**
- `postLikeStates`: Map存储每个帖子的点赞状态
- `postBookmarkStates`: Map存储每个帖子的收藏状态
- `postLikeCounts`: Map存储每个帖子的点赞数量

### 2. 新增处理函数
**详情页：**
- `handleLike()`: 详情页点赞/取消点赞处理
- `handleBookmark()`: 详情页收藏/取消收藏处理

**列表页：**
- `handleCardLike()`: 列表卡片点赞处理（包含事件冒泡阻止）
- `handleCardBookmark()`: 列表卡片收藏处理（包含事件冒泡阻止）

### 3. UI 组件
**详情页：**
- 在帖子详情模态框右上角添加点赞和收藏按钮
- 使用较大的按钮样式，点赞按钮显示数量

**列表页：**
- 在帖子卡片右下角添加小尺寸的点赞和收藏按钮
- 使用 Heart 和 Bookmark 图标
- 点赞按钮显示点赞数量（当数量大于0时）
- 按钮状态会根据是否已点赞/收藏改变样式
- 点击按钮不会触发卡片点击事件

### 4. 样式设计
**详情页按钮：**
- 默认状态：灰色边框，白色背景
- 点赞状态：红色主题
- 收藏状态：黄色主题
- 悬停效果：轻微上移和阴影

**列表页按钮：**
- 默认状态：透明背景，灰色图标
- 点赞状态：红色图标
- 收藏状态：橙色图标
- 悬停效果：浅色背景高亮
- 小尺寸设计，不影响卡片布局

**响应式设计：**
- 移动端适配
- 列表页按钮在移动端右对齐显示

## 文件修改

### src/pages/posts/index.tsx
1. 导入 Heart 和 Bookmark 图标
2. 添加状态变量
3. 添加处理函数（接口调用部分已注释，等待后端实现）
4. 修改帖子详情模态框结构，添加右上角操作区域

### src/pages/posts/index.module.css
**详情页样式：**
1. 修改 `.postDetailHeader` 样式
2. 新增 `.postDetailTopRightActions` 样式
3. 新增 `.postDetailActionButtons` 样式
4. 新增 `.postDetailActionBtn` 及其状态样式

**列表页样式：**
1. 修改 `.postFooter` 布局
2. 新增 `.postFooterLeft` 容器样式
3. 新增 `.cardActionButtons` 样式
4. 新增 `.cardActionBtn` 及其状态样式
5. 新增 `.actionCount` 数量显示样式

**响应式设计：**
6. 添加移动端适配样式

## 待实现的后端接口

```typescript
// 点赞接口
const likePost = async (postId: number) => {
  // POST /api/posts/{postId}/like
  // 返回: { success: boolean, message: string, data?: { liked: boolean, likeCount: number } }
}

// 收藏接口  
const bookmarkPost = async (postId: number) => {
  // POST /api/posts/{postId}/bookmark
  // 返回: { success: boolean, message: string, data?: { bookmarked: boolean } }
}

// 获取用户对帖子的点赞收藏状态
const getPostUserStatus = async (postId: number) => {
  // GET /api/posts/{postId}/user-status
  // 返回: { success: boolean, data: { liked: boolean, bookmarked: boolean, likeCount: number } }
}
```

## 使用说明

### 列表页操作：
1. 在帖子列表中，每个帖子卡片右下角都有点赞和收藏按钮
2. 点击按钮可以直接进行点赞/收藏操作，无需打开详情页
3. 点赞按钮会显示点赞数量（当数量大于0时）
4. 按钮点击不会触发卡片点击事件
5. 操作后会显示成功提示消息

### 详情页操作：
1. 打开任意帖子详情
2. 在右上角可以看到点赞（心形）和收藏（书签）按钮
3. 点击按钮会切换状态并显示相应的提示消息
4. 点赞按钮会显示点赞数量（模拟数据）
5. 关闭帖子详情后状态会重置

## 注意事项
- 目前所有的接口调用都被注释掉了，使用的是模拟的本地状态管理
- 需要在后端接口实现后取消注释相关代码
- 建议在获取帖子详情时同时获取用户的点赞收藏状态
- 考虑添加防抖处理避免重复点击
